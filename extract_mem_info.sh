#!/bin/bash

# 脚本功能：从messages文件中提取最近一次MEM-info到pages hwpoisoned的内容
# 作者：AI Assistant
# 创建日期：$(date '+%Y-%m-%d %H:%M:%S')
#
# 使用方法：
#   ./extract_mem_info.sh [messages文件路径] [输出文件名]
#   如果不提供参数，将使用默认路径

# 默认配置
DEFAULT_MESSAGES_FILE="/home/<USER>/Desktop/新建文件夹-3/messages"
DEFAULT_OUTPUT_FILE="latest_mem_info_$(date '+%Y%m%d_%H%M%S').txt"

# 参数处理
MESSAGES_FILE="${1:-$DEFAULT_MESSAGES_FILE}"
OUTPUT_FILE="${2:-$DEFAULT_OUTPUT_FILE}"

# 颜色定义（用于美化输出）
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "用法: $0 [messages文件路径] [输出文件名]"
    echo ""
    echo "参数说明："
    echo "  messages文件路径  - 要分析的messages文件路径（可选）"
    echo "  输出文件名        - 保存结果的文件名（可选）"
    echo ""
    echo "示例："
    echo "  $0                                    # 使用默认路径"
    echo "  $0 /var/log/messages                  # 指定messages文件"
    echo "  $0 /var/log/messages my_output.txt    # 指定输入和输出文件"
    echo ""
    echo "默认配置："
    echo "  默认messages文件: $DEFAULT_MESSAGES_FILE"
    echo "  默认输出文件: latest_mem_info_YYYYMMDD_HHMMSS.txt"
}

# 检查参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

print_info "开始执行内存信息提取脚本"
print_info "输入文件: $MESSAGES_FILE"
print_info "输出文件: $OUTPUT_FILE"

# 检查messages文件是否存在
if [ ! -f "$MESSAGES_FILE" ]; then
    print_error "找不到文件 $MESSAGES_FILE"
    print_info "请检查文件路径是否正确，或使用 -h 查看帮助"
    exit 1
fi

# 检查文件是否可读
if [ ! -r "$MESSAGES_FILE" ]; then
    print_error "文件 $MESSAGES_FILE 不可读，请检查权限"
    exit 1
fi

print_info "正在分析 $MESSAGES_FILE 文件..."

# 查找所有MEM-info的行号
print_info "搜索MEM-info记录..."
mem_info_lines=$(grep -n "Mem-Info:" "$MESSAGES_FILE" | cut -d: -f1)

if [ -z "$mem_info_lines" ]; then
    print_error "在文件中未找到MEM-info记录"
    print_info "请确认文件包含内存信息转储数据"
    exit 1
fi

# 统计找到的MEM-info记录数量
mem_info_count=$(echo "$mem_info_lines" | wc -l)
print_success "找到 $mem_info_count 条MEM-info记录"

# 获取最后一次MEM-info的行号
last_mem_info_line=$(echo "$mem_info_lines" | tail -1)
print_info "最近一次MEM-info记录位于第 $last_mem_info_line 行"

# 从最后一次MEM-info开始，查找对应的"pages hwpoisoned"行号
print_info "搜索对应的'pages hwpoisoned'记录..."
hwpoisoned_line=$(sed -n "${last_mem_info_line},\$p" "$MESSAGES_FILE" | grep -n "pages hwpoisoned" | head -1 | cut -d: -f1)

if [ -z "$hwpoisoned_line" ]; then
    print_error "未找到对应的'pages hwpoisoned'记录"
    print_warning "MEM-info记录可能不完整"
    exit 1
fi

# 计算实际的hwpoisoned行号（相对于整个文件）
actual_hwpoisoned_line=$((last_mem_info_line + hwpoisoned_line - 1))
print_success "找到对应的'pages hwpoisoned'记录，位于第 $actual_hwpoisoned_line 行"

# 计算要提取的行数
extract_lines=$((actual_hwpoisoned_line - last_mem_info_line + 1))
print_info "准备提取 $extract_lines 行内容（第 $last_mem_info_line 行到第 $actual_hwpoisoned_line 行）"

# 提取指定范围的内容
print_info "正在提取内容到 $OUTPUT_FILE ..."
sed -n "${last_mem_info_line},${actual_hwpoisoned_line}p" "$MESSAGES_FILE" > "$OUTPUT_FILE"

# 检查提取是否成功
if [ $? -eq 0 ] && [ -f "$OUTPUT_FILE" ]; then
    # 验证输出文件不为空
    if [ -s "$OUTPUT_FILE" ]; then
        print_success "提取成功！"
        print_info "输出文件: $OUTPUT_FILE"
        print_info "提取行数: $extract_lines"

        # 获取文件大小
        file_size=$(ls -lh "$OUTPUT_FILE" | awk '{print $5}')
        print_info "文件大小: $file_size"

        echo ""
        print_info "提取内容预览："
        echo "===================="
        head -3 "$OUTPUT_FILE"
        echo "..."
        tail -3 "$OUTPUT_FILE"
        echo "===================="

        print_success "脚本执行完成！"
    else
        print_error "输出文件为空，提取可能失败"
        exit 1
    fi
else
    print_error "提取失败，请检查权限和磁盘空间"
    exit 1
fi
